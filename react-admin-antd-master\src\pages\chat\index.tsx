import React, { useState } from "react";
import { Layout, Button } from "antd";
import { RouteConfig } from "@/types/route";
import {
  MessageOutlined,
  MenuOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import { Outlet } from "react-router-dom";
import { ChatStoreProvider } from "./stores";
import { ChatWindow } from "./components/ChatWindow";
import { ChatList } from "./components/ChatList";
import { useMobileScreen } from "./utils/hooks";

const { Sider, Content } = Layout;

// 聊天页面内容组件
const ChatPageContent: React.FC = () => {
  const [sidebarVisible, setSidebarVisible] = useState(true);
  const isMobile = useMobileScreen();

  const toggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  return (
    <Layout style={{ height: "100vh" }}>
      {/* 侧边栏 */}
      {sidebarVisible && (
        <Sider
          width={290}
          style={{
            backgroundColor: "#fff",
            borderRight: "1px solid #f0f0f0",
            position: isMobile ? "absolute" : "relative",
            zIndex: isMobile ? 1000 : "auto",
            height: "100%",
          }}
          collapsible={false}
        >
          <div
            style={{ height: "100%", display: "flex", flexDirection: "column" }}
          >
            {/* 聊天列表 */}
            <ChatList style={{ flex: 1 }} />
          </div>
        </Sider>
      )}

      {/* 主内容区 */}
      <Content style={{ display: "flex", flexDirection: "column" }}>
        {/* 顶部控制栏 */}
        {!sidebarVisible && (
          <div
            style={{
              padding: "8px 16px",
              borderBottom: "1px solid #f0f0f0",
              backgroundColor: "#fff",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={toggleSidebar}
              size="small"
              title="打开侧边栏"
            />
          </div>
        )}

        {/* 聊天窗口或子路由出口 */}
        <div style={{ flex: 1, overflow: "hidden" }}>
          <ChatWindow />
        </div>
      </Content>

      {/* 移动端遮罩 */}
      {isMobile && sidebarVisible && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.3)",
            zIndex: 999,
          }}
          onClick={toggleSidebar}
        />
      )}
    </Layout>
  );
};

// 主聊天页面组件（包含Provider）
const ChatPage: React.FC = () => {
  return (
    <ChatStoreProvider>
      <ChatPageContent />
    </ChatStoreProvider>
  );
};

export default ChatPage;
export const routeConfig: RouteConfig = {
  title: "route.chat",
  icon: <MessageOutlined />,
  layout: false,
  sort: 0,
};
