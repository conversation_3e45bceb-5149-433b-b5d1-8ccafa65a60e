/* ChatList 组件样式 */

.chatListContainer {
  height: 100%;
  background: #f7f7fc;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.newChatButton {
  background-color: #7b4ffe;
  border-color: #7b4ffe;

  &:hover {
    background-color: #6a3fee;
    border-color: #6a3fee;
  }
}

.divider {
  margin: 12px 0;
}

.navigationButtons {
  width: 100%;
}

.navigationButton {
  text-align: left;
  justify-content: flex-start;

  &.active {
    background-color: #7b4ffe;
    border-color: #7b4ffe;
    color: #fff;
  }

  &.inactive {
    background-color: transparent;
    border-color: #d9d9d9;
    color: #666;
  }
}

.searchInput {
  // 使用默认样式
}

.chatListContent {
  flex: 1;
  overflow: auto;
  padding: 16px;
  .emptyList {
    margin-top: 60px;
  }

  .chatListItem {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 0;
    height: 50px;
    padding-top: 4px;
    padding-right: 8px;
    padding-bottom: 4px;
    padding-left: 8px;
    gap: 8px;
    opacity: 1;
    border-radius: 8px;

    &.active {
      background-color: #ffffff;
      box-shadow: 0px 0px 3px 1px rgb(129 129 129 / 50%);
    }

    &.inactive {
      background-color: transparent;
      border-left: 3px solid transparent;
    }

    &:hover {
      background-color: #f8f8f8;
    }
    :global .ant-list-item-meta {
      display: flex;
      align-items: center;
    }
  }

  .messageIcon {
    font-size: 16px;
    // color: #7b4ffe;
  }

  .sessionTitle {
    &.active {
      // color: #7b4ffe;
      font-weight: 600;
    }

    &.inactive {
      color: #333;
      font-weight: 400;
    }
  }

  .sessionDescription {
    margin-bottom: 4px;
  }

  .sessionMeta {
    // 使用默认样式
  }

  .moreButton {
    opacity: 0.6;

    &:hover {
      opacity: 1;
    }
  }
}

.editInput {
  // 使用默认样式，点击事件阻止冒泡在JS中处理
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 12px;
  }

  .chatListItem {
    padding: 10px 12px;
  }
}
