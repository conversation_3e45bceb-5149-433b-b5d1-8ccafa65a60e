/* ChatList 组件样式 */

.chatListContainer {
  height: 100%;
  background: #f7f7fc;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.newChatButton {
  background-color: #7b4ffe;
  border-color: #7b4ffe;

  &:hover {
    background-color: #6a3fee;
    border-color: #6a3fee;
  }
}

.divider {
  margin: 12px 0;
}

.navigationButtons {
  width: 100%;
}

.navigationButton {
  text-align: left;
  justify-content: flex-start;

  &.active {
    background-color: #7b4ffe;
    border-color: #7b4ffe;
    color: #fff;
  }

  &.inactive {
    background-color: transparent;
    border-color: #d9d9d9;
    color: #666;
  }
}

.searchInput {
  // 使用默认样式
}

.chatListContent {
  flex: 1;
  overflow: auto;
  padding: 16px 16px 20px 16px; /* 增加底部 padding 确保最后一项边框完全显示 */
  .emptyList {
    margin-top: 60px;
  }

  .chatListItem {
    padding: 10px 14px;
    background-color: #fff;
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease; /* 包含边框和阴影的过渡效果 */
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    border: 2px solid transparent;
    position: relative;

    &.active {
      border-color: #7b4ffe;
      background-color: #ffffff;
      box-shadow: 0px 0px 3px 1px rgb(129 129 129 / 50%);
      margin-bottom: 12px; /* 激活状态时增加底部边距，为阴影留出空间 */
    }

    &.inactive {
      border-left: 3px solid transparent;
    }

    &:hover {
      background-color: #f8f8f8;
    }
    :global .ant-list-item-meta {
      display: flex;
      align-items: center;
    }
  }

  .messageIcon {
    font-size: 16px;
    // color: #7b4ffe;
  }

  .sessionTitle {
    &.active {
      // color: #7b4ffe;
      font-weight: 600;
    }

    &.inactive {
      color: #333;
      font-weight: 400;
    }
  }

  .sessionDescription {
    margin-bottom: 4px;
  }

  /* .sessionMeta 使用默认样式 */

  .moreButton {
    opacity: 0.6;

    &:hover {
      opacity: 1;
    }
  }
}

.editInput {
  // 使用默认样式，点击事件阻止冒泡在JS中处理
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 12px;
  }

  .chatListItem {
    padding: 10px 12px;
  }
}
