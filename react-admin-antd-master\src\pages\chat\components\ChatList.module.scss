/* ChatList 组件样式 */

.chatListContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.newChatButton {
  background-color: #7b4ffe;
  border-color: #7b4ffe;
  
  &:hover {
    background-color: #6a3fee;
    border-color: #6a3fee;
  }
}

.divider {
  margin: 12px 0;
}

.navigationButtons {
  width: 100%;
}

.navigationButton {
  text-align: left;
  justify-content: flex-start;
  
  &.active {
    background-color: #7b4ffe;
    border-color: #7b4ffe;
    color: #fff;
  }
  
  &.inactive {
    background-color: transparent;
    border-color: #d9d9d9;
    color: #666;
  }
}

.searchInput {
  // 使用默认样式
}

.chatListContent {
  flex: 1;
  overflow: auto;
}

.emptyList {
  margin-top: 60px;
}

.chatListItem {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.active {
    background-color: #f3f0ff;
    border-left: 3px solid #7b4ffe;
  }
  
  &.inactive {
    background-color: transparent;
    border-left: 3px solid transparent;
  }
  
  &:hover {
    background-color: #f8f8f8;
  }
}

.messageIcon {
  font-size: 16px;
  color: #7b4ffe;
}

.sessionTitle {
  &.active {
    color: #7b4ffe;
    font-weight: 600;
  }
  
  &.inactive {
    color: #333;
    font-weight: 400;
  }
}

.sessionDescription {
  margin-bottom: 4px;
}

.sessionMeta {
  // 使用默认样式
}

.moreButton {
  opacity: 0.6;
  
  &:hover {
    opacity: 1;
  }
}

.editInput {
  // 使用默认样式，点击事件阻止冒泡在JS中处理
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 12px;
  }
  
  .chatListItem {
    padding: 10px 12px;
  }
}
