import React from "react";
import { Card, Row, Col, Statistic, Progress, Typography, Space } from "antd";
import {
  BarChartOutlined,
  MessageOutlined,
  ClockCircleOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { RouteConfig } from "@/types/route";
import { useChatStoreOnly } from "../stores";

const { Title, Text } = Typography;

// 数据分析页面组件
const AnalyticsPage: React.FC = observer(() => {
  const chatStore = useChatStoreOnly();

  // 计算统计数据
  const totalSessions = chatStore.sessions.length;
  const totalMessages = chatStore.sessions.reduce(
    (sum, session) => sum + session.messages.length,
    0
  );
  const avgMessagesPerSession =
    totalSessions > 0 ? Math.round(totalMessages / totalSessions) : 0;

  // 计算今日消息数
  const today = new Date().toDateString();
  const todayMessages = chatStore.sessions.reduce((sum, session) => {
    return (
      sum +
      session.messages.filter(
        (msg) => new Date(msg.date).toDateString() === today
      ).length
    );
  }, 0);

  // 计算活跃度
  const recentSessions = chatStore.sessions.filter((session) => {
    const sessionDate = new Date(session.lastUpdate);
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    return sessionDate > weekAgo;
  }).length;

  const activityRate =
    totalSessions > 0 ? Math.round((recentSessions / totalSessions) * 100) : 0;

  return (
    <div style={{ padding: "24px", height: "100%", overflow: "auto" }}>
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        <Card>
          <Title level={3}>
            <BarChartOutlined style={{ marginRight: "8px" }} />
            数据分析
          </Title>
          <Text type="secondary">查看您的聊天数据统计和使用情况分析</Text>
        </Card>

        {/* 统计卡片 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总会话数"
                value={totalSessions}
                prefix={<MessageOutlined />}
                valueStyle={{ color: "#3f8600" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总消息数"
                value={totalMessages}
                prefix={<UserOutlined />}
                valueStyle={{ color: "#1890ff" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="今日消息"
                value={todayMessages}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: "#cf1322" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="平均消息/会话"
                value={avgMessagesPerSession}
                valueStyle={{ color: "#722ed1" }}
              />
            </Card>
          </Col>
        </Row>

        {/* 详细分析 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <Card title="活跃度分析">
              <Space
                direction="vertical"
                size="middle"
                style={{ width: "100%" }}
              >
                <div>
                  <Text strong>近7天活跃会话</Text>
                  <div style={{ marginTop: "8px" }}>
                    <Progress
                      percent={activityRate}
                      status="active"
                      format={() => `${recentSessions}/${totalSessions}`}
                    />
                  </div>
                </div>
                <div>
                  <Text type="secondary">活跃度: {activityRate}%</Text>
                </div>
              </Space>
            </Card>
          </Col>
          <Col xs={24} md={12}>
            <Card title="使用习惯">
              <Space direction="vertical" size="middle">
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Text>最活跃时段:</Text>
                  <Text strong>全天候</Text>
                </div>
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Text>平均会话长度:</Text>
                  <Text strong>{avgMessagesPerSession} 条消息</Text>
                </div>
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Text>使用频率:</Text>
                  <Text strong>
                    {totalSessions > 10
                      ? "高频用户"
                      : totalSessions > 3
                      ? "中频用户"
                      : "新用户"}
                  </Text>
                </div>
              </Space>
            </Card>
          </Col>
        </Row>

        {/* 趋势分析 */}
        <Card title="使用趋势">
          <div style={{ textAlign: "center", padding: "40px" }}>
            <BarChartOutlined style={{ fontSize: "48px", color: "#d9d9d9" }} />
            <div style={{ marginTop: "16px" }}>
              <Text type="secondary">图表功能开发中...</Text>
            </div>
          </div>
        </Card>
      </Space>
    </div>
  );
});

export default AnalyticsPage;

export const routeConfig: RouteConfig = {
  title: "数据分析",
  icon: <BarChartOutlined />,
  layout: false,
  sort: 3,
};
