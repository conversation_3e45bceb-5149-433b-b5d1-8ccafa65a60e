/* Antd 组件样式覆盖 */
.ant-menu{
  background: var(--menu-bg) !important;
}
.ant-menu-horizontal {
  border-bottom: none !important;
  background: var(--menu-bg) !important;
  line-height: 40px !important;
  width: 100% !important;
  
  .ant-menu-title-content {
    color: var(--text-primary) !important;
    position: relative;
    
    /* 自定义悬浮下划线 */
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      right: 50%;
      bottom: -16px; /* 调整下划线位置 */
      height: 3px;
      background: var(--primary-color);
      border-radius: 1.5px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      opacity: 0;
    }
  }
  
  .ant-menu-submenu-title {
    color: var(--text-primary) !important;
  }
  
  /* 悬浮效果 */
  .ant-menu-item:hover,
  .ant-menu-submenu:hover {
    color: var(--text-primary) !important;
    .ant-menu-title-content::after {
      left: 0;
      right: 0;
      opacity: 1;
    }
  }
  
  /* 选中效果 */
  .ant-menu-item-selected {
    .ant-menu-title-content::after {
      left: 0;
      right: 0;
      opacity: 1;
    }
  }
}

/* 子菜单弹出层样式 */
.ant-menu-submenu-popup {
  .ant-menu {
    background: var(--bg-primary) !important;
    border: 1px solid var(--border-primary) !important;
    padding: 4px !important;
    
    /* 灵动风格 */

    
    .ant-menu-item {
      min-width: 120px !important;
      margin: 2px 4px !important;
      padding: 0 12px !important;
      border-radius: var(--radius) !important;
      
      .ant-menu-title-content {
        color: var(--text-primary) !important;
      }
      
      &:hover {
        background: var(--bg-hover) !important;
        color: var(--text-primary) !important;
        .ant-menu-title-content {
          color: var(--primary-color) !important;
        }
      }
      
      &.ant-menu-item-selected {
        background: var(--bg-active) !important;
        .ant-menu-title-content {
          color: var(--primary-color) !important;
        }
      }
    }
    
    /* 多级子菜单 */
    .ant-menu-submenu {
      .ant-menu-submenu-title {
        min-width: 120px !important;
        margin: 2px 4px !important;
        padding: 0 12px !important;
        border-radius: var(--radius) !important;
        color: var(--text-primary) !important;
        
        &:hover {
          background: var(--bg-hover) !important;
          color: var(--primary-color) !important;
          
          .ant-menu-submenu-arrow {
            color: var(--primary-color) !important;
          }
        }
      }
      
      &.ant-menu-submenu-selected > .ant-menu-submenu-title {
        color: var(--primary-color) !important;
        background: var(--bg-active) !important;
        
        .ant-menu-submenu-arrow {
          color: var(--primary-color) !important;
        }
      }
    }
  }
}



/* 输入框样式 */
.ant-input {
  background: var(--bg-primary);
  border-color: var(--border-primary);
  color: var(--text-primary);
  
  &::placeholder {
    color: var(--text-disabled);
  }
}

/* 确保所有组件文字颜色统一 */
/* .ant-menu,
.ant-dropdown-menu,
.ant-btn,
.ant-input,
.ant-select {
  color: var(--text-primary);
} */

/* 下拉菜单样式 */
.ant-dropdown {
  .ant-dropdown-menu {
    background: var(--bg-primary) !important;
    border: 1px solid var(--border-primary) !important;
    padding: 4px !important;
    border-radius: var(--radius) !important;
    

    
    .ant-dropdown-menu-item {
      margin: 2px 0 !important;
      padding: 5px 12px !important;
      border-radius: var(--radius) !important;
      color: var(--text-primary) !important;
      transition: all 0.2s !important;
      
      .anticon {
        color: var(--text-secondary) !important;
      }
      
      &:hover {
        background: var(--bg-hover) !important;
        color: var(--primary-color) !important;
        
        .anticon {
          color: var(--primary-color) !important;
        }
      }
    }
    
    .ant-dropdown-menu-item-divider {
      background-color: var(--border-primary) !important;
      margin: 4px 0 !important;
    }
  }
}

/* Tab 样式覆盖 */
.ant-tabs-card {
  .ant-tabs-nav {
    margin-bottom: 0 !important;
    
    &::before {
      border: none !important;
    }
    
    .ant-tabs-tab {
      border: none !important;
      background: transparent !important;
      padding: 4px 8px !important;
      border-radius: var(--radius) !important;
      transition: all 0.2s !important;
      margin: 0 2px !important;
      
      &:hover {
        background: var(--bg-hover) !important;
      }
      
      &.ant-tabs-tab-active {
        background: var(--bg-active) !important;
        
        .ant-tabs-tab-btn {
          color: var(--primary-color) !important;
        }
      }
      
      .ant-tabs-tab-remove {
        margin-left: 4px;
        padding: 2px;
        border-radius: 50%;
        
        &:hover {
          background: rgba(0, 0, 0, 0.04);
          color: var(--error);
        }
      }
    }
  }
} 

