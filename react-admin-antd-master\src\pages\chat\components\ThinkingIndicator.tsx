import React, { useState, useEffect } from "react";
import styles from "./ThinkingIndicator.module.scss";

interface ThinkingIndicatorProps {
  fontSize: number;
}

export const ThinkingIndicator: React.FC<ThinkingIndicatorProps> = ({
  fontSize,
}) => {
  const [currentText, setCurrentText] = useState("");
  const [dotCount, setDotCount] = useState(0);

  const thinkingTexts = [
    "正在思考",
    "分析问题中",
    "整理思路",
    "准备回复",
    "生成回答",
  ];

  useEffect(() => {
    const textInterval = setInterval(() => {
      const randomText =
        thinkingTexts[Math.floor(Math.random() * thinkingTexts.length)];
      setCurrentText(randomText);
    }, 2000);

    const dotInterval = setInterval(() => {
      setDotCount((prev) => (prev + 1) % 4);
    }, 500);

    return () => {
      clearInterval(textInterval);
      clearInterval(dotInterval);
    };
  }, []);

  return (
    <div className={styles.thinkingContainer}>
      <div className={styles.contentWrapper}>
        <div className={styles.indicatorContent} style={{ fontSize: fontSize }}>
          <div className={styles.dotsContainer}>
            <span className={styles.dot}></span>
            <span className={styles.dot}></span>
            <span className={styles.dot}></span>
          </div>
          <div
            className={styles.textContent}
            style={{ fontSize: fontSize * 0.75 }}
          >
            {currentText}
            {"·".repeat(dotCount)}
          </div>
        </div>
      </div>
    </div>
  );
};
