import React, { useState } from "react";
import {
  List,
  Button,
  Input,
  Space,
  Typography,
  Dropdown,
  Modal,
  message,
  Empty,
  Divider,
} from "antd";
import {
  PlusOutlined,
  SearchOutlined,
  MoreOutlined,
  DeleteOutlined,
  EditOutlined,
  MessageOutlined,
  HistoryOutlined,
  Bar<PERSON>hartOutlined,
  FileTextOutlined,
  CommentOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { useNavigate, useLocation } from "react-router-dom";
import { useChatStoreOnly, useConfigStore } from "../stores";
import { ChatSession } from "../types";
import styles from "./ChatList.module.scss";

const { Text } = Typography;
const { Search } = Input;

interface ChatListProps {
  onSelectSession?: (sessionIndex: number) => void;
  style?: React.CSSProperties;
}

export const ChatList: React.FC<ChatListProps> = observer(
  ({ onSelectSession, style }) => {
    const chatStore = useChatStoreOnly();
    const configStore = useConfigStore();
    const navigate = useNavigate();
    const location = useLocation();
    const [searchKeyword, setSearchKeyword] = useState("");
    const [editingSessionId, setEditingSessionId] = useState<string | null>(
      null
    );
    const [editingTitle, setEditingTitle] = useState("");
    const [forceUpdate, setForceUpdate] = useState(0);

    // 过滤会话：排除临时会话，只显示有消息的正式会话
    const filteredSessions = chatStore.sessions
      .filter((session) => session.messages.length > 0) // 只显示有消息的会话
      .filter(
        (session) =>
          session.topic.toLowerCase().includes(searchKeyword.toLowerCase()) ||
          session.messages.some((msg) =>
            typeof msg.content === "string"
              ? msg.content.toLowerCase().includes(searchKeyword.toLowerCase())
              : false
          )
      );

    const handleNewChat = () => {
      chatStore.newSession();
      // 导航到聊天主界面（不带sessionId，表示新建聊天状态）
      navigate("/chat");
      message.success("新建聊天成功");
    };

    const handleCreateDemo = () => {
      chatStore.createDemoSession();
      message.success("演示会话已创建");
    };

    // 子路由导航处理
    const handleNavigateToSubRoute = (path: string) => {
      navigate(path);
    };

    // 子路由配置
    const subRoutes = [
      {
        key: "conversation",
        path: "/chat/conversation",
        label: "当前对话",
        icon: <CommentOutlined />,
      },
      {
        key: "history",
        path: "/chat/history",
        label: "聊天历史",
        icon: <HistoryOutlined />,
      },
      {
        key: "analytics",
        path: "/chat/analytics",
        label: "数据分析",
        icon: <BarChartOutlined />,
      },
      {
        key: "templates",
        path: "/chat/templates",
        label: "提示模板",
        icon: <FileTextOutlined />,
      },
    ];

    const handleSelectSession = (index: number) => {
      console.log(
        "Clicking session:",
        index,
        "Current:",
        chatStore.currentSessionIndex
      );

      const session = chatStore.sessions[index];
      if (session) {
        chatStore.selectSession(index);
        onSelectSession?.(index);

        // 导航到带有sessionId的聊天路由
        navigate(`/chat?sessionId=${session.id}`);
      }

      // 强制触发重新渲染
      setForceUpdate((prev) => prev + 1);
      setTimeout(() => {
        console.log(
          "After selection - Current session index:",
          chatStore.currentSessionIndex
        );
        setForceUpdate((prev) => prev + 1);
      }, 10);
    };

    const handleDeleteSession = (index: number, e: React.MouseEvent) => {
      e.stopPropagation();

      Modal.confirm({
        title: "确认删除",
        content: "确定要删除这个聊天会话吗？此操作不可恢复。",
        okText: "删除",
        okType: "danger",
        cancelText: "取消",
        onOk: () => {
          try {
            chatStore.deleteSession(index);
            message.success("聊天会话已删除");

            // 强制触发重新渲染，确保列表更新
            setTimeout(() => {
              // 如果有回调，通知父组件会话已切换
              if (onSelectSession && chatStore.sessions.length > 0) {
                onSelectSession(chatStore.currentSessionIndex);
              }
            }, 100);
          } catch (error) {
            console.error("删除会话失败:", error);
            message.error("删除会话失败，请重试");
          }
        },
      });
    };

    const handleEditTitle = (session: ChatSession, e: React.MouseEvent) => {
      e.stopPropagation();
      setEditingSessionId(session.id);
      setEditingTitle(session.topic);
    };

    const handleSaveTitle = () => {
      if (editingSessionId && editingTitle.trim()) {
        const sessionIndex = chatStore.sessions.findIndex(
          (s) => s.id === editingSessionId
        );
        if (sessionIndex >= 0) {
          chatStore.updateMessage(sessionIndex, -1, () => {
            chatStore.sessions[sessionIndex].topic = editingTitle.trim();
          });
          message.success("标题已更新");
        }
      }
      setEditingSessionId(null);
      setEditingTitle("");
    };

    const handleCancelEdit = () => {
      setEditingSessionId(null);
      setEditingTitle("");
    };

    const getSessionMenuItems = (session: ChatSession, index: number) => [
      {
        key: "edit",
        label: "编辑标题",
        icon: <EditOutlined />,
        onClick: (e: any) => handleEditTitle(session, e.domEvent),
      },
      {
        key: "delete",
        label: "删除会话",
        icon: <DeleteOutlined />,
        danger: true,
        onClick: (e: any) => handleDeleteSession(index, e.domEvent),
      },
    ];

    const formatLastUpdate = (timestamp: number) => {
      const now = Date.now();
      const diff = now - timestamp;

      if (diff < 60000) return "刚刚";
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
      return `${Math.floor(diff / 86400000)}天前`;
    };

    const getLastMessage = (session: ChatSession) => {
      if (session.messages.length === 0) return "暂无消息";

      const lastMessage = session.messages[session.messages.length - 1];
      const content =
        typeof lastMessage.content === "string"
          ? lastMessage.content
          : lastMessage.content
              .filter((c) => c.type === "text")
              .map((c) => c.text)
              .join("");

      return content.slice(0, 50) + (content.length > 50 ? "..." : "");
    };

    return (
      <div className={styles.chatListContainer} style={style}>
        {/* 头部操作区 */}
        <div className={styles.header}>
          <Space direction="vertical" style={{ width: "100%" }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleNewChat}
              block
              className={styles.newChatButton}
            >
              新建聊天
            </Button>

            {/* 子路由导航按钮 */}
            <Divider className={styles.divider}>功能导航</Divider>
            <Space
              direction="vertical"
              className={styles.navigationButtons}
              size="small"
            >
              {subRoutes.map((route) => (
                <Button
                  key={route.key}
                  type={
                    location.pathname === route.path ? "primary" : "default"
                  }
                  icon={route.icon}
                  onClick={() => handleNavigateToSubRoute(route.path)}
                  block
                  size="small"
                  className={`${styles.navigationButton} ${
                    location.pathname === route.path
                      ? styles.active
                      : styles.inactive
                  }`}
                >
                  {route.label}
                </Button>
              ))}
            </Space>

            <Divider style={{ margin: "12px 0" }}>聊天记录</Divider>
            <Search
              placeholder="搜索聊天记录..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              allowClear
            />
          </Space>
        </div>

        {/* 聊天列表 */}
        <div className={styles.chatListContent}>
          {filteredSessions.length === 0 ? (
            <Empty description="暂无聊天记录" className={styles.emptyList} />
          ) : (
            <List
              key={`chat-list-${forceUpdate}`}
              dataSource={filteredSessions}
              renderItem={(session) => {
                const actualIndex = chatStore.sessions.findIndex(
                  (s) => s.id === session.id
                );
                const isActive = actualIndex === chatStore.currentSessionIndex;
                const isEditing = editingSessionId === session.id;

                return (
                  <List.Item
                    key={`${session.id}-${actualIndex}-${isActive}`}
                    className={`${styles.chatListItem} ${
                      isActive ? styles.active : styles.inactive
                    }`}
                    onClick={() => {
                      if (!isEditing) {
                        console.log(
                          "List item clicked:",
                          actualIndex,
                          "isActive:",
                          isActive
                        );
                        handleSelectSession(actualIndex);
                      }
                    }}
                    actions={[
                      <div onClick={(e) => e.stopPropagation()}>
                        <Dropdown
                          key="more"
                          menu={{
                            items: getSessionMenuItems(session, actualIndex),
                          }}
                          trigger={["click"]}
                        >
                          <Button
                            type="text"
                            size="small"
                            icon={<MoreOutlined />}
                            style={{ opacity: 0.6 }}
                          />
                        </Dropdown>
                      </div>,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <MessageOutlined className={styles.messageIcon} />
                      }
                      title={
                        isEditing ? (
                          <Input
                            value={editingTitle}
                            onChange={(e) => setEditingTitle(e.target.value)}
                            onPressEnter={handleSaveTitle}
                            onBlur={handleSaveTitle}
                            onKeyDown={(e) => {
                              if (e.key === "Escape") {
                                handleCancelEdit();
                              }
                            }}
                            size="small"
                            autoFocus
                            onClick={(e) => e.stopPropagation()}
                          />
                        ) : (
                          <Text
                            strong={isActive}
                            className={`${styles.sessionTitle} ${
                              isActive ? styles.active : styles.inactive
                            }`}
                            style={{
                              fontSize: configStore.fontSize * 0.9,
                            }}
                          >
                            {session.topic}
                          </Text>
                        )
                      }
                      description={
                        <div>
                          <Text
                            type="secondary"
                            style={{
                              fontSize: configStore.fontSize * 0.8,
                              display: "block",
                              marginBottom: "4px",
                            }}
                          >
                            {getLastMessage(session)}
                          </Text>
                          <Text
                            type="secondary"
                            style={{ fontSize: configStore.fontSize * 0.7 }}
                          >
                            {formatLastUpdate(session.lastUpdate)} ·{" "}
                            {session.messages.length}条消息
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                );
              }}
            />
          )}
        </div>
      </div>
    );
  }
);
