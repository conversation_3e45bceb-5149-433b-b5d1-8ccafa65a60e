/* ThinkingIndicator 组件样式 */

.thinkingContainer {
  display: flex;
  gap: 8px;
  padding: 8px 16px;
  align-items: flex-end;
}

.contentWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: 100%;
  width: 100%;
  flex: 1;
}

.indicatorContent {
  max-width: 95%;
  min-width: 200px;
  color: #333;
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 8px;
  width: fit-content;
}

.dotsContainer {
  display: flex;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #52c41a;
  animation: thinking 1.4s infinite ease-in-out;
  
  &:nth-child(1) {
    animation-delay: -0.32s;
  }
  
  &:nth-child(2) {
    animation-delay: -0.16s;
  }
  
  &:nth-child(3) {
    animation-delay: 0s;
  }
}

.textContent {
  color: #999;
  margin-bottom: 4px;
  padding: 0 4px;
}

@keyframes thinking {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}
