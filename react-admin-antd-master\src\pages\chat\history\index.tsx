import React, { useState } from "react";
import { Card, List, Typography, Space, Tag, Button, Input, Empty } from "antd";
import {
  HistoryOutlined,
  SearchOutlined,
  MessageOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { RouteConfig } from "@/types/route";
import { useChatStoreOnly } from "../stores";

const { Title, Text } = Typography;
const { Search } = Input;

// 历史记录页面组件
const HistoryPage: React.FC = observer(() => {
  const chatStore = useChatStoreOnly();
  const [searchKeyword, setSearchKeyword] = useState("");

  const filteredSessions = chatStore.sessions.filter((session) =>
    session.topic.toLowerCase().includes(searchKeyword.toLowerCase())
  );

  const handleSelectSession = (sessionIndex: number) => {
    chatStore.selectSession(sessionIndex);
  };

  const handleDeleteSession = (sessionIndex: number, e: React.MouseEvent) => {
    e.stopPropagation();
    chatStore.deleteSession(sessionIndex);
  };

  return (
    <div style={{ padding: "24px", height: "100%", overflow: "auto" }}>
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        <Card>
          <Title level={3}>
            <HistoryOutlined style={{ marginRight: "8px" }} />
            聊天历史
          </Title>
          <Text type="secondary">查看和管理所有的聊天记录</Text>
        </Card>

        <Card>
          <Space direction="vertical" size="middle" style={{ width: "100%" }}>
            <Search
              placeholder="搜索聊天记录..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
            />

            {filteredSessions.length === 0 ? (
              <Empty description="暂无聊天记录" />
            ) : (
              <List
                dataSource={filteredSessions}
                renderItem={(session, index) => (
                  <List.Item
                    key={session.id}
                    style={{
                      cursor: "pointer",
                      padding: "16px",
                      border: "1px solid #f0f0f0",
                      borderRadius: "8px",
                      marginBottom: "8px",
                      backgroundColor:
                        chatStore.currentSessionIndex === index
                          ? "#f6ffed"
                          : "#fff",
                    }}
                    onClick={() => handleSelectSession(index)}
                    actions={[
                      <Button
                        key="delete"
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={(e) => handleDeleteSession(index, e)}
                      />,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <MessageOutlined
                          style={{ fontSize: "20px", color: "#1890ff" }}
                        />
                      }
                      title={
                        <Space>
                          <Text strong>{session.topic}</Text>
                          <Tag color="blue">
                            {session.messages.length} 条消息
                          </Tag>
                        </Space>
                      }
                      description={
                        <Space direction="vertical" size="small">
                          <Text type="secondary">
                            最后更新:{" "}
                            {new Date(session.lastUpdate).toLocaleString()}
                          </Text>
                          {session.messages.length > 0 && (
                            <Text type="secondary" ellipsis>
                              最后消息:{" "}
                              {typeof session.messages[
                                session.messages.length - 1
                              ].content === "string"
                                ? session.messages[
                                    session.messages.length - 1
                                  ].content.slice(0, 50) + "..."
                                : "多媒体消息"}
                            </Text>
                          )}
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </Space>
        </Card>
      </Space>
    </div>
  );
});

export default HistoryPage;

export const routeConfig: RouteConfig = {
  title: "聊天历史",
  icon: <HistoryOutlined />,
  layout: false,
  sort: 2,
};
