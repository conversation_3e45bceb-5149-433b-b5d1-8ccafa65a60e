import React from "react";
import { Card, Typography } from "antd";
// import { TestOutlined } from "@ant-design/icons";
import { RouteConfig } from "@/types/route";
import { MessageOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;

// 测试页面组件
const TestPage: React.FC = () => {
  return (
    <div style={{ padding: "24px", height: "100%", overflow: "auto" }}>
      <Card>
        <Title level={3}>
          {/* <TestOutlined style={{ marginRight: "8px" }} /> */}
          测试页面
        </Title>
        <Text>这是一个测试页面，用于验证子路由是否正常工作。</Text>
      </Card>
    </div>
  );
};

export default TestPage;

export const routeConfig: RouteConfig = {
  title: "测试页面",
  icon: <MessageOutlined />,
  layout: false,
  sort: 5,
};
