import React, { useEffect, useRef } from "react";
import {
  Avatar,
  Button,
  Space,
  Tooltip,
  Dropdown,
  message as antdMessage,
} from "antd";
import {
  UserOutlined,
  RobotOutlined,
  CopyOutlined,
  DeleteOutlined,
  MoreOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { ChatMessage } from "../types";
import { Markdown } from "./Markdown";
import {
  getMessageTextContent,
  getMessageImages,
  copyToClipboard,
} from "../utils/message";
import { useConfigStore } from "../stores";
import styles from "./MessageList.module.scss";
import { ThinkingIndicator } from "./ThinkingIndicator";

interface MessageListProps {
  messages: ChatMessage[];
  onDeleteMessage?: (messageId: string) => void;
  onEditMessage?: (messageId: string, newContent: string) => void;
  loading?: boolean;
}

interface MessageItemProps {
  message: ChatMessage;
  onDelete?: (messageId: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  fontSize: number;
  fontFamily: string;
  loading: boolean;
}

const MessageItem: React.FC<MessageItemProps> = observer(
  ({ message, onDelete, onEdit, fontSize, fontFamily, loading = false }) => {
    const isUser = message.role === "user";
    const isAssistant = message.role === "assistant";
    const isSystem = message.role === "system";

    const textContent = getMessageTextContent(message);
    const images = getMessageImages(message);
    const configStore = useConfigStore();
    const handleCopy = () => {
      copyToClipboard(textContent);
      antdMessage.success("已复制到剪贴板");
    };

    const handleDelete = () => {
      if (onDelete) {
        onDelete(message.id);
      }
    };

    const menuItems = [
      {
        key: "copy",
        label: "复制",
        icon: <CopyOutlined />,
        onClick: handleCopy,
      },
      {
        key: "delete",
        label: "删除",
        icon: <DeleteOutlined />,
        onClick: handleDelete,
        danger: true,
      },
    ];

    const getAvatarIcon = () => {
      if (isUser) return <UserOutlined />;
      if (isAssistant) return <RobotOutlined />;
      return <UserOutlined />;
    };

    const getAvatarColor = () => {
      if (isUser) return "#1890ff";
      if (isAssistant) return "#52c41a";
      return "#faad14";
    };

    // 计算动态宽度
    const calculateDynamicWidth = () => {
      if (!isAssistant || !message.streaming) {
        return "95%"; // 非AI回复或已完成的消息使用固定宽度
      }

      // 根据内容长度和类型计算宽度
      const contentLength = textContent.length;
      const minWidthPercent = 25; // 最小宽度百分比
      const maxWidthPercent = 95; // 最大宽度百分比

      // 检查内容类型，调整增长速度
      let growthRate = 1.5; // 基础增长率

      if (textContent.includes("```")) {
        growthRate = 3; // 代码块增长更快
      } else if (textContent.includes("|")) {
        growthRate = 2.5; // 表格增长较快
      } else if (textContent.includes("\n")) {
        growthRate = 2; // 多行文本增长较快
      }

      // 使用对数函数让增长更自然
      const widthPercent = Math.min(
        maxWidthPercent,
        minWidthPercent + Math.log(contentLength + 1) * growthRate * 3
      );

      return `${Math.max(minWidthPercent, widthPercent)}%`;
    };

    // 气泡框样式
    const getBubbleStyle = () => {
      const dynamicWidth = calculateDynamicWidth();

      const baseStyle = {
        maxWidth: dynamicWidth, // 使用动态宽度
        // minWidth: "200px", // 增加最小宽度
        padding: "12px 16px",
        borderRadius: "18px",
        wordBreak: "break-word" as const,
        position: "relative" as const,
        fontSize,
        fontFamily,
        lineHeight: 1.4,
        // 添加过渡动画，流式回复时更快的动画
        transition: message.streaming
          ? "all 0.2s ease-out"
          : "all 0.3s ease-out",
      };

      if (isUser) {
        return {
          ...baseStyle,
          // backgroundColor: "#1890ff",
          color: "#fff",
          marginLeft: "auto",
          borderTopRightRadius: "6px",

          borderBottomRightRadius: "18px",
        };
      } else {
        return {
          ...baseStyle,
          backgroundColor: "#E8EAFC",
          color: "#333",
          marginRight: "0",
          borderBottomLeftRadius: "6px",
          border: "1px solid #e8e8e8",
        };
      }
    };

    return (
      <div
        className={`${styles.messageItem} message-item ${message.role}`}
        style={{
          display: "flex",
          flexDirection: isUser ? "row-reverse" : "row",
          gap: "8px",
          padding: "8px 16px",
          alignItems: "flex-start",
        }}
      >
        {/* 头像 */}
        <Avatar
          size={32}
          style={{
            backgroundColor: getAvatarColor(),
            flexShrink: 0,
          }}
          icon={getAvatarIcon()}
        />

        {/* 消息气泡 */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: isUser ? "flex-end" : "flex-start",
            maxWidth: "100%", // 容器占满可用空间
            minWidth: 0,
            width: "100%", // 确保容器能够充分利用空间
            flex: 1, // 弹性布局
          }}
        >
          {/* 时间戳 */}
          <div
            style={{
              fontSize: fontSize * 0.75,
              color: "#999",
              marginBottom: "4px",
              padding: "0 4px",
            }}
          >
            {message.date}
          </div>

          {/* 消息内容气泡 */}
          <div
            className={`${styles.messageBubble} ${
              isUser ? styles.userBubble : styles.assistantBubble
            } ${isAssistant && message.streaming ? styles.streaming : ""}`}
            style={getBubbleStyle()}
          >
            {/* 图片内容 */}
            {images.length > 0 && (
              <div style={{ marginBottom: textContent ? "8px" : 0 }}>
                <Space wrap size={4}>
                  {images.map((image, index) => (
                    <img
                      key={index}
                      src={image}
                      alt={`消息图片 ${index + 1}`}
                      style={{
                        maxWidth: "150px",
                        maxHeight: "150px",
                        borderRadius: "8px",
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        window.open(image, "_blank");
                      }}
                    />
                  ))}
                </Space>
              </div>
            )}

            {/* 文本内容 */}
            {textContent && (
              <div
                className={styles.messageContent}
                style={{
                  animation: message.streaming
                    ? "textAppear 0.3s ease-out"
                    : "none",
                }}
              >
                <Markdown
                  content={textContent}
                  loading={message.streaming}
                  fontSize={fontSize}
                  fontFamily={fontFamily}
                />
              </div>
            )}

            {/* 工具调用结果 */}
            {message.tools && message.tools.length > 0 && (
              <div style={{ marginTop: "8px" }}>
                {message.tools.map((tool, index) => (
                  <div
                    key={tool.id || index}
                    style={{
                      backgroundColor: isUser
                        ? "rgba(255,255,255,0.2)"
                        : "#f0f0f0",
                      border: `1px solid ${
                        isUser ? "rgba(255,255,255,0.3)" : "#e0e0e0"
                      }`,
                      borderRadius: "8px",
                      padding: "8px",
                      marginBottom: "4px",
                      fontSize: fontSize * 0.85,
                    }}
                  >
                    <div
                      style={{
                        fontWeight: "bold",
                        marginBottom: "4px",
                        color: tool.isError
                          ? "#ff4d4f"
                          : isUser
                          ? "#fff"
                          : "#52c41a",
                      }}
                    >
                      🔧 {tool.function?.name || "工具调用"}
                      {tool.isError && " (错误)"}
                    </div>
                    {tool.function?.arguments && (
                      <div
                        style={{
                          fontSize: fontSize * 0.75,
                          color: isUser ? "rgba(255,255,255,0.8)" : "#666",
                          marginBottom: "4px",
                        }}
                      >
                        参数: {tool.function.arguments}
                      </div>
                    )}
                    {tool.content && (
                      <div style={{ fontSize: fontSize * 0.8 }}>
                        {tool.isError ? (
                          <span style={{ color: "#ff4d4f" }}>
                            {tool.errorMsg || tool.content}
                          </span>
                        ) : (
                          tool.content
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* 错误状态 */}
            {message.isError && (
              <div
                style={{
                  backgroundColor: isUser ? "rgba(255,77,79,0.2)" : "#fff2f0",
                  border: `1px solid ${
                    isUser ? "rgba(255,77,79,0.4)" : "#ffccc7"
                  }`,
                  borderRadius: "8px",
                  padding: "6px 8px",
                  marginTop: "8px",
                  color: "#ff4d4f",
                  fontSize: fontSize * 0.85,
                }}
              >
                ⚠️ 消息发送失败，请重试
              </div>
            )}
          </div>

          {/* 底部操作区域：思考指示器在最左边，操作按钮在最右边 */}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginTop: "8px",
              width: "100%",
              minHeight: "32px", // 确保有足够的高度
            }}
          >
            {/* 左侧：思考指示器 */}
            <div style={{ flex: "0 0 auto" }}>
              {loading && !isUser && (
                <ThinkingIndicator fontSize={configStore.fontSize} />
              )}
            </div>

            {/* 右侧：操作按钮 */}
            <div
              className={styles.messageActions}
              style={{
                flex: "0 0 auto",
              }}
            >
              <Dropdown
                menu={{ items: menuItems }}
                trigger={["click"]}
                placement={isUser ? "bottomLeft" : "bottomRight"}
              >
                <Button
                  type="text"
                  size="small"
                  icon={<MoreOutlined />}
                  style={{
                    fontSize: "12px",
                    color: "#999",
                    padding: "2px 4px",
                    height: "auto",
                  }}
                />
              </Dropdown>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

export const MessageList: React.FC<MessageListProps> = observer(
  ({ messages, onDeleteMessage, onEditMessage, loading = false }) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const configStore = useConfigStore();
    // 自动滚动到底部
    useEffect(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
      }
    }, [messages.length]);

    if (messages.length === 0) {
      return (
        <div
          className={styles.emptyState}
          style={{
            fontSize: configStore.fontSize,
          }}
        >
          <div className={styles.emptyStateIcon}>
            <div className={styles.emptyStateIconInner}>COZE</div>
          </div>
          <div
            className={styles.emptyStateTitle}
            style={{
              fontSize: configStore.fontSize * 1.5,
            }}
          >
            网站开发专家
          </div>
          <div
            className={styles.emptyStateDescription}
            style={{
              fontSize: configStore.fontSize * 0.95,
            }}
          >
            由IT专家团队开发的网站开发专家
            Agent，无需编程，通过对话即可快速构建各种网站应用。
          </div>
          <div
            className={styles.emptyStatePrompt}
            style={{
              fontSize: configStore.fontSize * 0.9,
            }}
          >
            有什么问题可以问我呢？
          </div>
        </div>
      );
    }

    return (
      <div className={styles.messageList}>
        {messages.map((message, index) => {
          // 只有最后一条 AI 消息且正在加载时才显示思考指示器
          const isLastMessage = index === messages.length - 1;
          const isAssistantMessage = message.role === "assistant";
          const shouldShowLoading =
            loading && isLastMessage && isAssistantMessage;

          return (
            <MessageItem
              key={message.id}
              message={message}
              loading={shouldShowLoading}
              onDelete={onDeleteMessage}
              onEdit={onEditMessage}
              fontSize={configStore.fontSize}
              fontFamily="inherit"
            />
          );
        })}

        <div ref={messagesEndRef} />
      </div>
    );
  }
);
